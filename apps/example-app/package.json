{"name": "example-app", "version": "1.0.0", "private": true, "description": "Example application using monorepo packages", "main": "dist/index.js", "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "echo \"No tests yet\""}, "dependencies": {"@monorepo/ui-components": "file:../../packages/ui-components", "@monorepo/utils": "file:../../packages/utils", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3"}}