import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import dts from 'rollup-plugin-dts';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Create a base Rollup configuration for packages
 * @param {string} packageDir - The package directory path
 * @param {Object} options - Additional options
 * @returns {Array} Array of Rollup configurations
 */
export function createConfig(packageDir, options = {}) {
  const pkg = JSON.parse(readFileSync(join(packageDir, 'package.json'), 'utf8'));
  const external = [
    ...Object.keys(pkg.dependencies || {}),
    ...Object.keys(pkg.peerDependencies || {}),
    /^node:/,
    /^@types\//
  ];

  const input = join(packageDir, 'src/index.ts');
  
  const baseConfig = {
    input,
    external,
    plugins: [
      resolve({
        preferBuiltins: true,
      }),
      commonjs(),
      typescript({
        tsconfig: join(packageDir, 'tsconfig.json'),
        declaration: false,
        declarationMap: false,
        sourceMap: true,
        ...options.typescript
      }),
    ],
  };

  const configs = [];

  // ESM build
  if (pkg.module || pkg.exports?.['.']?.import) {
    configs.push({
      ...baseConfig,
      output: {
        file: join(packageDir, pkg.module || pkg.exports['.'].import),
        format: 'esm',
        sourcemap: true,
      },
    });
  }

  // CommonJS build
  if (pkg.main || pkg.exports?.['.']?.require) {
    configs.push({
      ...baseConfig,
      output: {
        file: join(packageDir, pkg.main || pkg.exports['.'].require),
        format: 'cjs',
        sourcemap: true,
        exports: 'auto',
      },
    });
  }

  // Type definitions
  if (pkg.types || pkg.exports?.['.']?.types) {
    configs.push({
      input,
      external,
      plugins: [dts()],
      output: {
        file: join(packageDir, pkg.types || pkg.exports['.'].types),
        format: 'esm',
      },
    });
  }

  return configs;
}

export default createConfig;
